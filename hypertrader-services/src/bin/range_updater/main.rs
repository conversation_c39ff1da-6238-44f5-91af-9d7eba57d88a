use anyhow::Result;
use hypertrader_hyperliquid::client::data;
use hypertrader_models::kline::interval::KlineInterval;
use hypertrader_utils::init::ensure_inited;
use hypertrader_data::cex::hyperliquid::HyperliquidDataManager;
use optimizer::TableOptimizer;
use service::RangeService;
use backfill_ohlc::BackfillOhlcService;

// use hypertrader_services::common;
use tracing::{info, error};

mod service;
mod optimizer;
mod backfill_ohlc;

// Helper function to create backfill service
async fn create_backfill_service(days: i64, batch: usize) -> Result<BackfillOhlcService> {
    let range_service = RangeService::new(false, 3).await?;
    BackfillOhlcService::new(range_service, days, batch).await
}


#[tokio::main]
async fn main() -> Result<()> {
    // Initialize environment
    ensure_inited();
    // Initial fetch of data for all intervals (run once)
    // let durations = KlineInterval::all();

    info!("Starting Range Updater Service");

    // Initialize Hyperliquid data manager
    let hyperliquid_data_manager: HyperliquidDataManager =
        HyperliquidDataManager::new_without_postgres(false).await?;

    hyperliquid_data_manager.init().await?;

    // Get subscribed coins
    let subscribed_coins = hyperliquid_data_manager
        .kline_manager
        .get_subscribed_coins();
        // Henry: Commented out to get all coins
        // .get_subscribed_coins_limit(5);

    hyperliquid_data_manager
        .kline_manager
        .set_subscribed_coins(subscribed_coins.clone());

    // Subscribe to candle events for each coin and interval
    for coin in &subscribed_coins {
        // Subscribe to 1-minute candles for each coin then using Materialized Views (MV) of clickhouseDB to aggregate data to other intervals
        match hyperliquid_data_manager.client.subscribe_coin(coin.name.clone(), KlineInterval::OneMinute).await {
            Ok(subscription_id) => {
                info!("Subscribed to {} candles for interval {:?}, ID: {}", coin.name, KlineInterval::OneMinute, subscription_id);
            },
            Err(e) => {
                error!("Failed to subscribe to {} candles for interval {:?}: {}", coin.name, KlineInterval::OneMinute, e);
            }
        }
    }

    // Start the WebSocket listener for real-time updates
    hyperliquid_data_manager.kline_manager.run().await?;

     // Service to fetch and update OHLC data -- DO NOT REMOVE
    // Configure and start the historical data update service
    let max_fetch_minutes = 60 * 24 * 7; // 7 days
    let days_to_fetch = 7; // Number of days to fetch
    let batch_size = 8; // Process 8 coins at a time

    // ----- FETCH AND UPDATE ALL HISTORY OHLC DATA SERVICE -----

    // // Get subscribed coins
    // let subscribed_coins = hyperliquid_data_manager
    //     .kline_manager
    //     .get_subscribed_coins();
    //     // Henry: Commented out to get all coins
    //     // .get_subscribed_coins_limit(5);

    // // Create the Range service
    // let service = RangeService::new(false, max_fetch_minutes).await?;

    // // Fetch and update OHLC data with batch processing
    // service.fetch_and_update_ohlc_data(subscribed_coins.clone(), days_to_fetch, batch_size).await?;

    // ----- END OF FETCH AND UPDATE OHLC DATA SERVICE -----

    // ============================================

    // ----- BACKFILL OHLC 1M SERVICE -----

    // Create a separate RangeService for backfill

    let backfill_range_service = RangeService::new(false, 3).await?;

    // Create the backfill service
    let backfill_service = BackfillOhlcService::new(
        backfill_range_service.clone(),
        days_to_fetch,
        batch_size
    ).await?;

    // Update materialized views once at startup
    info!("Updating materialized views at startup");
    backfill_service.update_materialized_views().await?;

    // After creating the backfill service
    info!("Updating materialized views for klines to ohlc direct mapping");
    backfill_service.update_klines_materialized_views().await?;

    // Schedule periodic updates in a separate task
    let coins_for_backfill_ohlc = subscribed_coins.clone();
    tokio::spawn(async move {
        if let Err(e) = backfill_service.run_periodic_ohlc_updates(coins_for_backfill_ohlc).await {
            error!("Backfill service error: {}", e);
        }
    });

    // ----- END OF BACKFILL 1M SERVICE -----

    // ----- BACKFILL VOLUME AND NUMBER OF TRADES SERVICE -----

    // Create services for different intervals with descriptive names
    let volume_service = create_backfill_service(days_to_fetch, batch_size).await?;

    // Schedule periodic volume updates in a separate task
    let volume_service_clone = volume_service.clone();

    // Schedule periodic volume updates in a separate task
    let coins_for_backfill_volume = subscribed_coins.clone();
    tokio::spawn(async move {
        if let Err(e) = volume_service.run_periodic_volume_backfills(
            coins_for_backfill_volume,
            volume_service_clone,
        ).await {
            error!("Backfill volume service error: {}", e);
        }
    });
    // ============================================

    // ----- TABLE OPTIMIZER -----

    // let table_optimizer = TableOptimizer::new().await?;

    // // Start the optimization scheduler
    // table_optimizer.run().await?;

    // ----- END OF TABLE OPTIMIZER -----

    // ----- BACKFILL 5000 CANDLES ----- DO NOT REMOVE THIS COMMENT -----

    // let durations: Vec<KlineInterval> = KlineInterval::all()
    //     .into_iter()
    //     .filter(|interval| *interval != KlineInterval::OneMinute)
    //     .collect();

    // for duration in durations {
    //     hyperliquid_data_manager
    //         .kline_manager
    //         .fetch_subscribed_coins_klines(&duration, 8)
    //         .await?;
    // }

    // // Create the backfill service
    // let backfill_service = BackfillOhlcService::new(
    //     backfill_range_service,
    //     days_to_fetch,
    //     batch_size
    // ).await?;

    // backfill_service.backfill_all_timeframe_volumes(subscribed_coins.clone()).await?;

    // ----- END OF BACKFILL 5000 CANDLES -----


    // Keep the main thread running
    tokio::signal::ctrl_c().await?;
    info!("Received shutdown signal, stopping service...");

    Ok(())
}
