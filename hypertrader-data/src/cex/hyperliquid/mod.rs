use std::sync::Arc;
use dashmap::DashMap;
use hyperliquid_rust_sdk::{Address, CandleData, DisplayConfig, Message, TitleOption, UserStateResponse, H160};
use hypertrader_dbs::{DatabaseConnection, managers::prices::PricesManager};
use hypertrader_hyperliquid::client::data::DataClient;
use hypertrader_models::kline::interval::KlineInterval;
use ws_candle_helper::{process_minute_change, should_process_new_minute, update_best_candle};

use crate::models::{price::PriceState, user::UserState};
pub mod klines;
pub mod ws_candle_helper;

#[derive(Clone)]
pub struct HyperliquidDataManager {
    pub client: Arc<DataClient>,
    pub is_testnet: bool,

    pub db: DatabaseConnection,
    pub prices_manager: PricesManager,

    pub user_states: Arc<DashMap<H160, UserState>>, // user balance state
    pub price_state: Arc<tokio::sync::RwLock<PriceState>>,

    pub address_subscription_id_map: dashmap::DashMap<Address, u32>,
    pub kline_manager: klines::DataKlineManager,

    // key: (coin, kline_interval), value: subscription_id
    pub subscribed_coins: DashMap<(String, KlineInterval), u32>,

    // Map to track best candles by (coin, minute timestamp, interval)
    // key: (coin, timestamp_minute, interval), value: CandleData with highest num_trades
    pub best_candles: Arc<DashMap<(String, u64, String), CandleData>>,
}

impl HyperliquidDataManager {
    pub async fn new_without_postgres(is_testnet: bool) -> anyhow::Result<Self> {
        let db = hypertrader_dbs::DatabaseConnection::default();
        Self::new(db, is_testnet).await
    }

    pub async fn new(db: DatabaseConnection, is_testnet: bool) -> anyhow::Result<Self> {
        let (client, mut receiver) = DataClient::new(is_testnet).await;
        let price_state = Arc::new(tokio::sync::RwLock::new(PriceState::default()));

        let user_states: Arc<DashMap<H160, UserState>> = Arc::new(DashMap::new());
        let user_states_clone = user_states.clone();
        let kline_manager_clone = klines::DataKlineManager::new(client.clone().into()).await?;

        let prices_manager = PricesManager::new(db.clone());
        // let prices_manager_clone = prices_manager.clone();

        // Map to track best candles
        let best_candles = Arc::new(DashMap::<(String, u64, String), CandleData>::new());
        let best_candles_clone = best_candles.clone();

        // Track the current minute to detect changes
        let mut current_minute = 0;
        // Track the last processed minute to prevent duplicate processing
        let mut processed_minutes = std::collections::HashSet::new();

        tokio::spawn(async move {
            // // this loop ends when we unsubscribe

            loop {
                let message = receiver.recv().await.unwrap();

                match message {
                    Message::AllMids(_all_mids) => {
                        // let mids_len = all_mids.data.mids.len();
                        // let res = prices_manager_clone
                        //     .insert_prices_map(all_mids.data.mids)
                        //     .await;

                        // if res.is_err() {
                        //     tracing::error!("Failed to insert prices: {:#?}", res);
                        // } else {
                        //     tracing::info!("Received all mids: {:#?}", mids_len);
                        // }

                        // let price_state = PriceState::from(all_mids);
                        // *price_state_clone.write().await = price_state;
                    }

                    Message::WebData2(web_data2) => {
                        let address = web_data2.data.user;
                        let value = web_data2.data.clearinghouse_state;

                        // value.cmd_display();

                        // Use custom config
                        let config = DisplayConfig {
                            max_items: Some(5),                // Only show first 3 records
                            title_option: TitleOption::Simple, // Use simple title
                        };
                        value.cmd_display_with_config(&config);

                        if let Some(user_state) = user_states_clone.get_mut(&address) {
                            user_state.update_with_value(value).await;
                        }
                    }

                    Message::User(user_events) => {
                        println!("user_events: {:#?}", user_events);
                        // We haven't seen the first mid price event yet, so just continue
                    }
                    Message::Candle(candle) => {
                        let coin = candle.data.coin.clone();
                        let interval = candle.data.interval.clone();
                        let minute_timestamp = candle.data.time_open;

                        // Process minute change if needed
                        if should_process_new_minute(current_minute, minute_timestamp, &processed_minutes) {
                            let previous_minute = current_minute;
                            current_minute = minute_timestamp;

                            // Mark the minute as processed and save the candles
                            process_minute_change(
                                previous_minute,
                                minute_timestamp,
                                &mut processed_minutes,
                                &best_candles_clone,
                                &kline_manager_clone
                            ).await;
                        } else if current_minute == 0 {
                            // Initialize the first minute
                            current_minute = minute_timestamp;
                        }

                        // Update the best candle map
                        update_best_candle(
                            coin,
                            minute_timestamp,
                            interval,
                            candle.data.clone(),
                            &best_candles_clone
                        );
                    }
                    _ => {
                        tracing::error!("Unsupported message type: {:#?}", message);
                    }
                }
            }
        });

        let client = Arc::new(client);

        Ok(Self {
            kline_manager: klines::DataKlineManager::new(client.clone()).await?,
            client,
            is_testnet,
            user_states,
            price_state,
            db,
            prices_manager,
            address_subscription_id_map: dashmap::DashMap::new(),
            subscribed_coins: DashMap::new(),
            best_candles: best_candles,
        })
    }

    /// subscribe coins and kline
    /// input:
    /// - coins: names of coins, eg. ["BTC", "ETH", "SOL"]
    /// - kline_interval: KlineInterval, eg. KlineInterval::OneMinute
    pub async fn subscribe_coins(
        &self,
        coins: Vec<String>,
        kline_interval: KlineInterval,
    ) -> anyhow::Result<()> {
        let mut tasks = vec![];
        for coin in &coins {
            tasks.push(
                self.client
                    .subscribe_coin(coin.clone(), kline_interval.clone()),
            );
        }
        let kline_sub_ids = futures::future::try_join_all(tasks).await?;
        for (coin, kline_sub_id) in coins.iter().zip(kline_sub_ids) {
            self.subscribed_coins
                .insert((coin.clone(), kline_interval.clone()), kline_sub_id);
        }

        Ok(())
    }

    pub async fn init(&self) -> anyhow::Result<()> {
        // init cron tasks
        self.init_cron_tasks().await?;

        // init ws receiver

        // update price
        self.client.subscribe_all_mids().await?;

        // for test
        if std::env::var("DISABLE_KLINE").unwrap_or("0".to_string()) != "1" {
            // init kline manager
            self.kline_manager.init().await?;
        }

        Ok(())
    }

    pub async fn list_subscribed_addresses(&self) -> anyhow::Result<Vec<Address>> {
        let addresses = self
            .address_subscription_id_map
            .iter()
            .map(|data| data.key().clone())
            .collect();
        Ok(addresses)
    }

    pub async fn subscribe_user(&self, user: H160) -> anyhow::Result<()> {
        let subscription_id = self.client.subscribe_user(user).await?;
        self.address_subscription_id_map
            .insert(user, subscription_id);
        Ok(())
    }

    pub async fn unsubscribe_user(&self, user: H160) -> anyhow::Result<()> {
        let subscription_id = self.address_subscription_id_map.remove(&user);
        if let Some((_, subscription_id)) = subscription_id {
            self.client.unsubscribe(subscription_id).await?;
        }
        Ok(())
    }

    async fn init_cron_tasks(&self) -> anyhow::Result<()> {
        // Create scheduled task to update meta every 5 minutes
        let client_ref = self.client.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(5 * 60));

            loop {
                interval.tick().await;
                match client_ref.update_meta().await {
                    Ok(_) => tracing::info!("Successfully updated metadata"),
                    Err(err) => tracing::error!("Failed to update metadata: {}", err),
                }
            }
        });

        Ok(())
    }

    pub async fn update_user_state_with_value(
        &self,
        address: H160,
        value: UserStateResponse,
    ) -> anyhow::Result<()> {
        match self.user_states.get_mut(&address) {
            Some(user_state) => {
                user_state.update_with_value(value).await;
            }
            None => {
                let user_state = UserState::new(address, &self.client).await?;
                self.user_states.insert(address, user_state);
            }
        }

        Ok(())
    }

    pub async fn update_user_state(&self, address: H160) -> anyhow::Result<()> {
        match self.user_states.get_mut(&address) {
            Some(user_state) => {
                user_state.update(&self.client).await?;
            }
            None => {
                let user_state = UserState::new(address, &self.client).await?;
                self.user_states.insert(address, user_state);
            }
        }
        Ok(())
    }
}
